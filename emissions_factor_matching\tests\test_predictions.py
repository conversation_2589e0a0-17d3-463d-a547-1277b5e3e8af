from unittest import TestCase
from utils import logger
from emissions_factor_matching.predictions import (
    get_cas_number,
    predict_input_category,
    predict_enhanced_input_category,
    map_isic_classification,
)


cas_number_validations = [
    ("Sodium Lauryl Sulfate", "151-21-3"),
    ("Sodium Carbonate", "497-19-8"),
    ("Mango", None),
    ("<PERSON><PERSON>berry", None),
    ("Glycerin", "56-81-5"),
    ("Cocamidopropyl betaine", "61789-40-0"),
    ("Palm Tree", None),
    ("Natural Flavoring", None),
    ("Water", "7732-18-5"),
    ("Fragrance", None),
    ("Ethanol", "64-17-5"),
    ("Sodium Chloride", "7647-14-5"),
    ("Acetic Acid", "64-19-7"),
    ("Sugar", "57-50-1"),
    ("Benzene", "71-43-2"),
    ("Citric Acid", "77-92-9"),
    ("Aspirin", "50-78-2"),
    ("Caf<PERSON>ine", "58-08-2"),
    ("Baking Soda", "144-55-8"),
    ("<PERSON><PERSON><PERSON>", "8028-52-2"),
    ("Toluene", "108-88-3"),
    ("Xylene", "1330-20-7"),
    ("Formaldehyde", "50-00-0"),
    ("Bleach", "7681-52-9"),
    ("Hydrochloric Acid", "7647-01-0"),
    ("Nitric Acid", "7697-37-2"),
    ("Sulfuric Acid", "7664-93-9"),
    ("Aluminum Oxide", "1344-28-1"),
    ("Copper Sulfate", "7758-98-7"),
    ("Ammonia", "7664-41-7"),
    ("Lactic Acid", "50-21-5"),
    ("Stearic Acid", "57-11-4"),
    ("Lemon Juice", "68916-88-1"),
    ("Olive Oil", "8001-25-0"),
    ("Corn Oil", "8001-30-7"),
    ("Soybean Oil", "8001-22-7"),
    ("Peppermint Oil", "8006-90-4"),
    ("Castor Oil", "8001-79-4"),
    ("Zinc Oxide", "1314-13-2"),
    ("Silver Nitrate", "7761-88-8"),
    ("Silicone Oil", "68083-14-7"),
    ("Ethylene Glycol", "107-21-1"),
    ("Propylene Glycol", "57-55-6"),
    ("Glycerol", "56-81-5"),
    ("Essential Oils", None),
    ("Flavors", None),
    ("Fragrances", None),
    ("Herbs", None),
    ("Plants", None),
    ("Minerals", None),
    ("Petroleum", "8002-05-9")
]

# Removed outdated ISIC validation data since the functions they tested no longer exist
# The new AI-assisted approach uses different methods for ISIC classification

input_category_validations = [
    ("car door", "PRODUCT"),
    ("baking soda", "CHEMICAL"),
    ("sodium chloride", "CHEMICAL"),
    ("teflon", "CHEMICAL"),
    ("zip", "PRODUCT"),
    ("mason jar", "PRODUCT"),
]

class PredictionsTest(TestCase):
    def test_get_cas_number(self):
        accuracy_threshold_percent = 0.9
        hard_score = 0
        soft_score = 0

        for chemical_name, validation_cas_number in cas_number_validations:
            cas_number = get_cas_number(chemical_name)
            if cas_number == "NONE":
                cas_number = None

            if cas_number == validation_cas_number:
                logger.info(f"Expected: {validation_cas_number}, Predicted: {cas_number}")
                hard_score += 1
                soft_score += 1
            elif not cas_number:
                logger.warning(f"Expected: {validation_cas_number}, Unable to predict CAS Number")
                soft_score += 1
            else:
                logger.error(f"Expected: {validation_cas_number}, Predicted: {cas_number}")

        logger.info(f"Sensitivity (avoid false positives): {soft_score} / {len(cas_number_validations)} correct")
        self.assertGreaterEqual(soft_score, len(cas_number_validations))

        logger.info(f"Total Accuracy: {soft_score} / {len(cas_number_validations)} correct")
        self.assertGreaterEqual(hard_score, len(cas_number_validations) * accuracy_threshold_percent)

    def test_input_category_prediction(self):
        correct = 0
        for input_str, validation_category in input_category_validations:
            category = predict_input_category(input_str)

            if category == validation_category:
                logger.info(f"Expected: {validation_category}, Predicted: {category}")
                correct += 1
            else:
                logger.error(f"Expected: {validation_category}, Predicted: {category}")

        logger.info(f"{correct} / {len(input_category_validations)} correct")
        self.assertEqual(correct, len(input_category_validations))

    def test_map_isic_classification(self):
        """Test the new ISIC classification mapping function"""
        test_cases = [
            ("Water treatment", ["3600"]),  # Water supply
            ("Sodium Chloride mining", ["0893"]),  # Salt mining
            ("Calcium Chloride production", ["2013"]),  # Chemical manufacturing
            ("Electricity generation", ["3510"]),  # Electric power generation
        ]

        for user_query, expected_codes in test_cases:
            result = map_isic_classification("CHEMICAL", [], user_query)
            logger.info(f"Query: {user_query}, Expected codes: {expected_codes}, Got: {result}")
            # Note: This is a basic test - the AI may return different but valid codes
            self.assertIsInstance(result, list)
            self.assertGreater(len(result), 0, f"Should return at least one ISIC code for: {user_query}")

    def test_enhanced_input_category(self):
        """Test the enhanced input category prediction using dict input"""
        test_cases = [
            ("car door", "PRODUCT"),
            ("baking soda", "CHEMICAL"),
            ("sodium chloride", "CHEMICAL"),
            ("transportation service", "TRANSPORT"),
        ]

        for query, expected_category in test_cases:
            # Use dict format which the function supports
            request_dict = {
                "user_query_primary": query,
                "user_query_secondary": None,
                "lca_lifecycle_stage": None,
                "iso_code": None
            }
            result = predict_enhanced_input_category(request_dict)
            logger.info(f"Query: {query}, Expected: {expected_category}, Got: {result}")
            self.assertIsNotNone(result, f"Should return a category for: {query}")
            # Note: Enhanced prediction may return more specific categories
