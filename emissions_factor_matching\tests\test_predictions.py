from unittest import TestCase
from utils import logger
from emissions_factor_matching.predictions import (
    get_cas_number,
    predict_enhanced_input_category,
    map_isic_classification,
)


cas_number_validations = [
    ("Sodium Lauryl Sulfate", "151-21-3"),
    ("Sodium Carbonate", "497-19-8"),
    ("Mango", None),
    ("<PERSON><PERSON>berry", None),
    ("Glycerin", "56-81-5"),
    ("Cocamidopropyl betaine", "61789-40-0"),
    ("Palm Tree", None),
    ("Natural Flavoring", None),
    ("Water", "7732-18-5"),
    ("Fragrance", None),
    ("Ethanol", "64-17-5"),
    ("Sodium Chloride", "7647-14-5"),
    ("Acetic Acid", "64-19-7"),
    ("Sugar", "57-50-1"),
    ("Benzene", "71-43-2"),
    ("Citric Acid", "77-92-9"),
    ("Aspirin", "50-78-2"),
    ("Caf<PERSON>ine", "58-08-2"),
    ("Baking Soda", "144-55-8"),
    ("<PERSON>egar", "8028-52-2"),
    ("Toluene", "108-88-3"),
    ("Xylene", "1330-20-7"),
    ("Formaldehyde", "50-00-0"),
    ("Bleach", "7681-52-9"),
    ("Hydrochloric Acid", "7647-01-0"),
    ("Nitric Acid", "7697-37-2"),
    ("Sulfuric Acid", "7664-93-9"),
    ("Aluminum Oxide", "1344-28-1"),
    ("Copper Sulfate", "7758-98-7"),
    ("Ammonia", "7664-41-7"),
    ("Lactic Acid", "50-21-5"),
    ("Stearic Acid", "57-11-4"),
    ("Lemon Juice", "68916-88-1"),
    ("Olive Oil", "8001-25-0"),
    ("Corn Oil", "8001-30-7"),
    ("Soybean Oil", "8001-22-7"),
    ("Peppermint Oil", "8006-90-4"),
    ("Castor Oil", "8001-79-4"),
    ("Zinc Oxide", "1314-13-2"),
    ("Silver Nitrate", "7761-88-8"),
    ("Silicone Oil", "68083-14-7"),
    ("Ethylene Glycol", "107-21-1"),
    ("Propylene Glycol", "57-55-6"),
    ("Glycerol", "56-81-5"),
    ("Essential Oils", None),
    ("Flavors", None),
    ("Fragrances", None),
    ("Herbs", None),
    ("Plants", None),
    ("Minerals", None),
    ("Petroleum", "8002-05-9")
]

# Removed outdated ISIC validation data since the functions they tested no longer exist
# The new AI-assisted approach uses different methods for ISIC classification

# Removed outdated input_category_validations since the enhanced system now returns
# detailed subcategories (e.g., PRODUCT_AUTOMOTIVE_VEHICLE) instead of broad categories (e.g., PRODUCT)

class PredictionsTest(TestCase):
    def test_get_cas_number(self):
        accuracy_threshold_percent = 0.9
        hard_score = 0
        soft_score = 0

        for chemical_name, validation_cas_number in cas_number_validations:
            cas_number = get_cas_number(chemical_name)
            if cas_number == "NONE":
                cas_number = None

            if cas_number == validation_cas_number:
                logger.info(f"Expected: {validation_cas_number}, Predicted: {cas_number}")
                hard_score += 1
                soft_score += 1
            elif not cas_number:
                logger.warning(f"Expected: {validation_cas_number}, Unable to predict CAS Number")
                soft_score += 1
            else:
                logger.error(f"Expected: {validation_cas_number}, Predicted: {cas_number}")

        logger.info(f"Sensitivity (avoid false positives): {soft_score} / {len(cas_number_validations)} correct")
        self.assertGreaterEqual(soft_score, len(cas_number_validations))

        logger.info(f"Total Accuracy: {soft_score} / {len(cas_number_validations)} correct")
        self.assertGreaterEqual(hard_score, len(cas_number_validations) * accuracy_threshold_percent)

    # Removed test_input_category_prediction as it tested legacy functionality
    # The enhanced system now returns detailed subcategories instead of broad categories
    # Use test_enhanced_input_category for testing the current system

    def test_map_isic_classification(self):
        """Test the new LLM-based ISIC classification mapping function"""
        test_cases = [
            # (user_query, enhanced_category, modifiers, expected_section)
            ("Water treatment", "SERVICE_WASTE_TREATMENT", [], "E"),  # Water supply; sewerage, waste management
            ("Sodium Chloride mining", "CHEMICAL_INORGANIC_ACID", ["mining"], "B"),  # Mining and quarrying
            ("Calcium Chloride production", "CHEMICAL_INORGANIC_ACID", ["production"], "C"),  # Manufacturing
            ("Electricity generation", "SERVICE_ENERGY_ELECTRICITY", [], "D"),  # Electricity, gas, steam
            ("Road freight transport", "SERVICE_TRANSPORT_ROAD_FREIGHT", ["diesel"], "H"),  # Transportation and storage
        ]

        for user_query, enhanced_category, modifiers, expected_section in test_cases:
            result = map_isic_classification(enhanced_category, modifiers, user_query)
            logger.info(f"Query: '{user_query}' (Category: {enhanced_category}) -> Got: {result}")

            # Basic validation: should return a list
            self.assertIsInstance(result, list, f"Should return a list for: {user_query}")

            # Should return at least one ISIC code (unless it's a genuinely unmappable query)
            self.assertGreater(len(result), 0, f"Should return at least one ISIC code for: {user_query}")

            # Validate that returned codes are 4-digit strings
            for code in result:
                self.assertIsInstance(code, str, f"ISIC code should be string: {code}")
                self.assertEqual(len(code), 4, f"ISIC code should be 4 digits: {code}")
                self.assertTrue(code.isdigit(), f"ISIC code should be numeric: {code}")

            # Optional: Check if the returned codes are in the expected section
            # Note: This is flexible since LLM may return valid alternatives
            if expected_section and result:
                section_mapping = {
                    "A": range(100, 400),   # Agriculture, forestry and fishing
                    "B": range(500, 1000),  # Mining and quarrying
                    "C": range(1000, 3400), # Manufacturing
                    "D": range(3500, 3600), # Electricity, gas, steam
                    "E": range(3600, 3900), # Water supply; sewerage, waste management
                    "F": range(4100, 4400), # Construction
                    "G": range(4500, 4800), # Wholesale and retail trade
                    "H": range(4900, 5400), # Transportation and storage
                }

                expected_range = section_mapping.get(expected_section)
                if expected_range:
                    # At least one code should be in the expected section (flexible validation)
                    codes_in_section = [code for code in result if int(code) in expected_range]
                    if not codes_in_section:
                        logger.warning(f"Query '{user_query}': Expected section {expected_section}, but got codes {result}")
                        # Don't fail the test - just log a warning since LLM may have valid reasons

    def test_enhanced_input_category(self):
        """Test the enhanced input category prediction using dict input"""
        test_cases = [
            # Test cases with expected category prefixes (the system returns detailed subcategories)
            ("car door", "PRODUCT_"),  # Should return something like PRODUCT_AUTOMOTIVE_VEHICLE
            ("baking soda", "CHEMICAL_"),  # Should return something like CHEMICAL_INORGANIC_ACID
            ("sodium chloride", "CHEMICAL_"),  # Should return something like CHEMICAL_INORGANIC_ACID
            ("transportation service", "SERVICE_"),  # Should return something like SERVICE_TRANSPORT_*
            ("plastic bottle", "PRODUCT_"),  # Could be PRODUCT_PACKAGING_CONTAINER or CHEMICAL_POLYMER_PLASTIC
            ("cotton shirt", "PRODUCT_"),  # Should return something like PRODUCT_TEXTILE_CLOTHING
        ]

        for query, expected_prefix in test_cases:
            # Use dict format which the function supports
            request_dict = {
                "user_query_primary": query,
                "user_query_secondary": None,
                "lca_lifecycle_stage": None,
                "iso_code": None
            }
            result = predict_enhanced_input_category(request_dict)
            logger.info(f"Query: '{query}' -> Got: '{result}' (Expected prefix: '{expected_prefix}')")

            # Validate that we get a result
            self.assertIsNotNone(result, f"Should return a category for: {query}")

            # Validate that the result starts with the expected prefix
            self.assertTrue(
                result.startswith(expected_prefix),
                f"Query '{query}': Expected category starting with '{expected_prefix}', got '{result}'"
            )

            # Validate that the result is one of the valid detailed categories
            valid_categories = [
                # Chemical categories
                "CHEMICAL_ORGANIC_SOLVENT", "CHEMICAL_INORGANIC_ACID", "CHEMICAL_POLYMER_PLASTIC",
                "CHEMICAL_METAL_COMPOUND", "CHEMICAL_FUEL_ENERGY", "CHEMICAL_PHARMACEUTICAL",
                "CHEMICAL_AGRICULTURAL", "CHEMICAL_OTHER",
                # Product categories
                "PRODUCT_ELECTRONICS_DEVICE", "PRODUCT_AUTOMOTIVE_VEHICLE", "PRODUCT_TEXTILE_CLOTHING",
                "PRODUCT_CONSTRUCTION_MATERIAL", "PRODUCT_PACKAGING_CONTAINER", "PRODUCT_FOOD_BEVERAGE",
                "PRODUCT_FURNITURE_FIXTURE", "PRODUCT_MEDICAL_EQUIPMENT", "PRODUCT_OTHER",
                # Service categories
                "SERVICE_TRANSPORT_ROAD_FREIGHT", "SERVICE_TRANSPORT_ROAD_PASSENGER", "SERVICE_TRANSPORT_AIR",
                "SERVICE_TRANSPORT_MARITIME", "SERVICE_TRANSPORT_RAIL", "SERVICE_ENERGY_ELECTRICITY",
                "SERVICE_ENERGY_HEATING", "SERVICE_WASTE_TREATMENT", "SERVICE_CONSTRUCTION", "SERVICE_OTHER"
            ]

            self.assertIn(
                result,
                valid_categories,
                f"Query '{query}': Got invalid category '{result}'. Must be one of the predefined categories."
            )
