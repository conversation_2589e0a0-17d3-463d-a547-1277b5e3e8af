INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÜÇ Testing COMPLETE AI-assisted pipeline (Phases 1.1-1.9)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'truck transportation for heavy cargo' + 'diesel trucks over 32 tons' (ISO: US)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:16.984 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÄ» Phase 1.1 - Enhanced Category: SERVICE_TRANSPORT_ROAD_FREIGHT
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:21.645 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöì Phase 1.2 - Modifiers: ['diesel', 'truck', '>32t', 'heavy cargo']
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÅ¡ Phase 1.3 - ISIC Codes: ['4923']
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:24.056 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:21:24.057 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the delivery of heavy cargo. Operations involve compression ignition engines optimized for long-haul logistics, focusing on the use phase emissions of commercial trucking fleets within the freight transport sector classified under ISIC 4923.'
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôê Phase 1.4 - Augmented Query: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the delivery of heavy cargo. Operations involve compression ignition engines optimized for long-haul logistics, focusing on the use phase emissions of commercial trucking fleets within the freight transport sector classified under ISIC 4923.'
2025-05-28 11:21:24.145 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:869 - Found weight modifiers for transport: ['>32t']
2025-05-28 11:21:24.146 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:880 - Constructed dynamic filters with 4 conditions
2025-05-28 11:21:24.147 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:881 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöº Phase 1.5 - Dynamic Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
2025-05-28 11:21:24.148 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 11:21:24.149 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the delivery of heavy cargo. Operations involve compression ignition engines optimized for long-haul logistics, focusing on the use phase emissions of commercial trucking fleets within the freight transport sector classified under ISIC 4923.'
2025-05-28 11:21:24.150 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
2025-05-28 11:21:24.151 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 10 results
2025-05-28 11:21:24.152 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 0.11ms
2025-05-28 11:21:24.153 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 3 candidates
2025-05-28 11:21:24.154 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'transport, freight, lorry >32 metric ton' (distance: 0.1540, similarity: 0.8460)
2025-05-28 11:21:24.155 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 2: 'transport, freight, lorry 16-32 metric ton' (distance: 0.2670, similarity: 0.7330)
2025-05-28 11:21:24.156 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 3: 'transport, freight, lorry 7.5-16 metric ton' (distance: 0.3890, similarity: 0.6110)
2025-05-28 11:21:24.157 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 3 candidate objects
2025-05-28 11:21:24.158 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'transport, freight, lorry >32 metric ton' (distance: 0.1540)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöì Phase 1.6 - Found 3 candidates
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:30.605 | INFO     | completions:get_structured_completion:119 - Structured completion successful: RerankingResponse
2025-05-28 11:21:30.606 | INFO     | emissions_factor_matching.predictions:re_rank_candidates:695 - Re-ranking complete: Selected 'transport, freight, lorry >32 metric ton' with HIGH confidence
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒºá Phase 1.7 - LLM Re-ranking Complete: Selected 'transport, freight, lorry >32 metric ton' with HIGH confidence
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Phase 1.8 - Geography Matching & Record Retrieval - Starting
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Phase 1.8 Complete: Geography matched to 'US'
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôª Phase 1.9 - Response Assembly - Starting
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôª Phase 1.9 Complete: Assembled response with 2 recommendations
FINFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Testing complete pipeline with geography fallback
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'carbon steel production' (ISO: ZW)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:33.305 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:35.149 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:38.691 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:21:38.693 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Hot-rolled carbon steel production for industrial applications, focusing on processes in primary steel manufacturing under ISIC 2410. Includes steelmaking via basic oxygen furnaces or electric arc furnaces, followed by hot rolling operations to produce industrial-grade steel sheets, plates, and structural components. Emphasizes energy-intensive metallurgical processes, emissions from high-temperature operations, and material properties tailored for construction and heavy industry use.'
2025-05-28 11:21:38.708 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:880 - Constructed dynamic filters with 4 conditions
2025-05-28 11:21:38.709 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:881 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2410:Manufacture of basic iron and steel'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}, {'unit': {'$in': ['kg']}}]}
2025-05-28 11:21:38.710 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 11:21:38.710 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Hot-rolled carbon steel production for industrial applications, focusing on processes in primary steel manufacturing under ISIC 2410. Includes steelmaking via basic oxygen furnaces or electric arc furnaces, followed by hot rolling operations to produce industrial-grade steel sheets, plates, and structural components. Emphasizes energy-intensive metallurgical processes, emissions from high-temperature operations, and material properties tailored for construction and heavy industry use.'
2025-05-28 11:21:38.712 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2410:Manufacture of basic iron and steel'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}, {'unit': {'$in': ['kg']}}]}
2025-05-28 11:21:38.713 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 10 results
2025-05-28 11:21:38.714 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 0.09ms
2025-05-28 11:21:38.715 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 1 candidates
2025-05-28 11:21:38.716 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'steel production, carbon steel, hot-rolled' (distance: 0.2340, similarity: 0.7660)
2025-05-28 11:21:38.716 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 1 candidate objects
2025-05-28 11:21:38.717 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'steel production, carbon steel, hot-rolled' (distance: 0.2340)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:21:43.486 | INFO     | completions:get_structured_completion:119 - Structured completion successful: RerankingResponse
2025-05-28 11:21:43.488 | INFO     | emissions_factor_matching.predictions:re_rank_candidates:695 - Re-ranking complete: Selected 'steel production, carbon steel, hot-rolled' with HIGH confidence
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Geography fallback: ZW ΓåÆ GLO
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:Γ£à Complete pipeline with geography fallback test passed!
.INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöä Testing pipeline data flow integrity
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:Γ£à Pipeline data flow integrity test passed!
.
======================================================================
FAIL: test_complete_pipeline_phases_1_through_9 (emissions_factor_matching.tests.test_phase1_9_complete_integration.TestPhase1to9CompleteIntegration)
Test complete pipeline from Phase 1.1 through Phase 1.9 including geography matching and response assembly
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/unittest/mock.py", line 1379, in patched
    return func(*newargs, **newkeywargs)
  File "/home/<USER>/app/emissions_factor_matching/tests/test_phase1_9_complete_integration.py", line 275, in test_complete_pipeline_phases_1_through_9
    mock_geography_match.assert_called_with(
  File "/usr/local/lib/python3.10/unittest/mock.py", line 929, in assert_called_with
    raise AssertionError(_error_message()) from cause
AssertionError: expected call not found.
Expected: get_geography_activity_match('transport, freight, lorry >32 metric ton', 'US', 'transport, freight, lorry >32 metric ton', 'Ecoinvent 3.11')
Actual: get_geography_activity_match('transport, freight, lorry >32 metric ton', 'US', 'transport, freight, lorry >32 metric ton', None)

----------------------------------------------------------------------
Ran 3 tests in 28.015s

FAILED (failures=1)
load INSTRUCTOR_Transformer
max_seq_length  512
load INSTRUCTOR_Transformer
max_seq_length  512
Token has not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: read).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
