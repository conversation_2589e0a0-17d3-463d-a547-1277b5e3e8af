INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÜÇ Testing COMPLETE AI-assisted pipeline (Phases 1.1-1.9)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'truck transportation for heavy cargo' + 'diesel trucks over 32 tons' (ISO: US)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:38:08.032 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÄ» Phase 1.1 - Enhanced Category: SERVICE_TRANSPORT_ROAD_FREIGHT
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:38:13.806 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöì Phase 1.2 - Modifiers: ['diesel', 'truck', '>32t', 'heavy cargo']
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÅ¡ Phase 1.3 - ISIC Codes: ['4923']
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:38:18.060 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 08:38:18.061 | INFO     | emissions_factor_matching.predictions:augment_query_text:737 - Query augmentation successful: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the movement of heavy cargo. Operations involve compression ignition engines in commercial trucking fleets, focusing on the use phase of vehicle lifecycle. Relevant to logistics and goods transport under ISIC 4923, emphasizing long-haul and regional delivery scenarios.'
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôê Phase 1.4 - Augmented Query: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the movement of heavy cargo. Operations involve compression ignition engines in commercial trucking fleets, focusing on the use phase of vehicle lifecycle. Relevant to logistics and goods transport under ISIC 4923, emphasizing long-haul and regional delivery scenarios.'
EINFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Testing complete pipeline with geography fallback
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'carbon steel production' (ISO: ZW)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:38:20.288 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:38:22.409 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:38:25.057 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 08:38:25.058 | INFO     | emissions_factor_matching.predictions:augment_query_text:737 - Query augmentation successful: 'Manufacturing of hot-rolled carbon steel construction materials, focusing on steel production processes in ISIC 2410. Includes operations such as blast furnace ironmaking, basic oxygen steelmaking, and hot rolling for structural and industrial applications. Covers emissions from energy-intensive processes, alloying, and material shaping in steel mills.'
EINFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöä Testing pipeline data flow integrity
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:Γ£à Pipeline data flow integrity test passed!
.
======================================================================
ERROR: test_complete_pipeline_phases_1_through_9 (emissions_factor_matching.tests.test_phase1_9_complete_integration.TestPhase1to9CompleteIntegration)
Test complete pipeline from Phase 1.1 through Phase 1.9 including geography matching and response assembly
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/unittest/mock.py", line 1379, in patched
    return func(*newargs, **newkeywargs)
  File "/home/<USER>/app/emissions_factor_matching/tests/test_phase1_9_complete_integration.py", line 165, in test_complete_pipeline_phases_1_through_9
    filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
  File "/home/<USER>/app/emissions_factor_matching/predictions.py", line 774, in construct_dynamic_filters
    if classification.startswith(f"{code}:") or classification == code:
AttributeError: 'int' object has no attribute 'startswith'

======================================================================
ERROR: test_complete_pipeline_with_geography_fallback (emissions_factor_matching.tests.test_phase1_9_complete_integration.TestPhase1to9CompleteIntegration)
Test complete pipeline with geography fallback scenario
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/unittest/mock.py", line 1379, in patched
    return func(*newargs, **newkeywargs)
  File "/home/<USER>/app/emissions_factor_matching/tests/test_phase1_9_complete_integration.py", line 345, in test_complete_pipeline_with_geography_fallback
    filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
  File "/home/<USER>/app/emissions_factor_matching/predictions.py", line 774, in construct_dynamic_filters
    if classification.startswith(f"{code}:") or classification == code:
AttributeError: 'int' object has no attribute 'startswith'

----------------------------------------------------------------------
Ran 3 tests in 18.697s

FAILED (errors=2)
load INSTRUCTOR_Transformer
max_seq_length  512
load INSTRUCTOR_Transformer
max_seq_length  512
Token has not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: read).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
