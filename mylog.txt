INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÜÇ Testing COMPLETE AI-assisted pipeline (Phases 1.1-1.9)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'truck transportation for heavy cargo' + 'diesel trucks over 32 tons' (ISO: US)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:18.421 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÄ» Phase 1.1 - Enhanced Category: SERVICE_TRANSPORT_ROAD_FREIGHT
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:20.219 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöì Phase 1.2 - Modifiers: ['diesel', 'truck', '>32t', 'heavy cargo']
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÅ¡ Phase 1.3 - ISIC Codes: ['4923']
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:23.508 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:27:23.510 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the movement of heavy cargo. Operations involve the use of compression ignition engines in commercial trucking fleets, focusing on the use phase of vehicle lifecycle. Relevant to logistics and goods transport under ISIC 4923, emphasizing long-haul and regional delivery scenarios.'
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôê Phase 1.4 - Augmented Query: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the movement of heavy cargo. Operations involve the use of compression ignition engines in commercial trucking fleets, focusing on the use phase of vehicle lifecycle. Relevant to logistics and goods transport under ISIC 4923, emphasizing long-haul and regional delivery scenarios.'
2025-05-28 11:27:23.522 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:869 - Found weight modifiers for transport: ['>32t']
2025-05-28 11:27:23.524 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:880 - Constructed dynamic filters with 4 conditions
2025-05-28 11:27:23.524 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:881 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöº Phase 1.5 - Dynamic Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
2025-05-28 11:27:23.525 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 11:27:23.526 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks exceeding 32 tonnes gross vehicle weight for the movement of heavy cargo. Operations involve the use of compression ignition engines in commercial trucking fleets, focusing on the use phase of vehicle lifecycle. Relevant to logistics and goods transport under ISIC 4923, emphasizing long-haul and regional delivery scenarios.'
2025-05-28 11:27:23.527 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
2025-05-28 11:27:23.528 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 10 results
2025-05-28 11:27:23.528 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 0.08ms
2025-05-28 11:27:23.529 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 3 candidates
2025-05-28 11:27:23.530 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'transport, freight, lorry >32 metric ton' (distance: 0.1540, similarity: 0.8460)
2025-05-28 11:27:23.530 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 2: 'transport, freight, lorry 16-32 metric ton' (distance: 0.2670, similarity: 0.7330)
2025-05-28 11:27:23.531 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 3: 'transport, freight, lorry 7.5-16 metric ton' (distance: 0.3890, similarity: 0.6110)
2025-05-28 11:27:23.531 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 3 candidate objects
2025-05-28 11:27:23.532 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'transport, freight, lorry >32 metric ton' (distance: 0.1540)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöì Phase 1.6 - Found 3 candidates
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:30.119 | INFO     | completions:get_structured_completion:119 - Structured completion successful: RerankingResponse
2025-05-28 11:27:30.120 | INFO     | emissions_factor_matching.predictions:re_rank_candidates:695 - Re-ranking complete: Selected 'transport, freight, lorry >32 metric ton' with HIGH confidence
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒºá Phase 1.7 - LLM Re-ranking Complete: Selected 'transport, freight, lorry >32 metric ton' with HIGH confidence
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Phase 1.8 - Geography Matching & Record Retrieval - Starting
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Phase 1.8 Complete: Geography matched to 'US'
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôª Phase 1.9 - Response Assembly - Starting
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôª Phase 1.9 Complete: Assembled response with 2 recommendations
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÅå Final Selection: 'transport, freight, lorry >32 metric ton' (UUID: geo-matched-transport-1)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Geography: US
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÄ» Confidence: HIGH
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôï Recommendations: 2 alternatives
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÆí Explanation: The selected candidate, 'transport, freight, lorry >32 metric ton' (UUID: ef-transport-1), is the mo...
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:Γ£à COMPLETE pipeline (Phases 1.1-1.9) test passed!
.INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Testing complete pipeline with geography fallback
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'carbon steel production' (ISO: ZW)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:32.666 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:35.091 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:37.451 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:27:37.452 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Hot-rolled carbon steel production in industrial-scale facilities, focusing on processes such as steelmaking, rolling, and finishing. Includes operations in ISIC 2410 classified establishments, utilizing blast furnaces, basic oxygen furnaces, and continuous casting methods for manufacturing construction-grade steel products.'
2025-05-28 11:27:37.464 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:880 - Constructed dynamic filters with 4 conditions
2025-05-28 11:27:37.465 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:881 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2410:Manufacture of basic iron and steel'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}, {'unit': {'$in': ['kg']}}]}
2025-05-28 11:27:37.465 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 11:27:37.466 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Hot-rolled carbon steel production in industrial-scale facilities, focusing on processes such as steelmaking, rolling, and finishing. Includes operations in ISIC 2410 classified establishments, utilizing blast furnaces, basic oxygen furnaces, and continuous casting methods for manufacturing construction-grade steel products.'
2025-05-28 11:27:37.466 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2410:Manufacture of basic iron and steel'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}, {'unit': {'$in': ['kg']}}]}
2025-05-28 11:27:37.467 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 10 results
2025-05-28 11:27:37.468 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 0.09ms
2025-05-28 11:27:37.468 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 1 candidates
2025-05-28 11:27:37.469 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'steel production, carbon steel, hot-rolled' (distance: 0.2340, similarity: 0.7660)
2025-05-28 11:27:37.470 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 1 candidate objects
2025-05-28 11:27:37.470 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'steel production, carbon steel, hot-rolled' (distance: 0.2340)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 11:27:43.106 | INFO     | completions:get_structured_completion:119 - Structured completion successful: RerankingResponse
2025-05-28 11:27:43.107 | INFO     | emissions_factor_matching.predictions:re_rank_candidates:695 - Re-ranking complete: Selected 'steel production, carbon steel, hot-rolled' with MEDIUM confidence
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Geography fallback: ZW ΓåÆ GLO
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:Γ£à Complete pipeline with geography fallback test passed!
.INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöä Testing pipeline data flow integrity
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:Γ£à Pipeline data flow integrity test passed!
.
----------------------------------------------------------------------
Ran 3 tests in 25.338s

OK
load INSTRUCTOR_Transformer
max_seq_length  512
load INSTRUCTOR_Transformer
max_seq_length  512
Token has not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: read).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
