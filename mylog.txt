INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: hkunlp/instructor-large
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÜÇ Testing COMPLETE AI-assisted pipeline (Phases 1.1-1.9)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'truck transportation for heavy cargo' + 'diesel trucks over 32 tons' (ISO: US)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:43:50.388 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÄ» Phase 1.1 - Enhanced Category: SERVICE_TRANSPORT_ROAD_FREIGHT
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:43:54.249 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöì Phase 1.2 - Modifiers: ['diesel', 'truck', '>32t', 'heavy cargo']
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒÅ¡ Phase 1.3 - ISIC Codes: ['4923']
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:43:56.756 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 08:43:56.757 | INFO     | emissions_factor_matching.predictions:augment_query_text:737 - Query augmentation successful: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks with a gross vehicle weight exceeding 32 tonnes for the delivery of heavy cargo. Operations involve compression ignition engines optimized for long-haul logistics, focusing on the use phase emissions and energy consumption in commercial trucking fleets under ISIC sector 4923.'
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒôê Phase 1.4 - Augmented Query: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks with a gross vehicle weight exceeding 32 tonnes for the delivery of heavy cargo. Operations involve compression ignition engines optimized for long-haul logistics, focusing on the use phase emissions and energy consumption in commercial trucking fleets under ISIC sector 4923.'
2025-05-28 08:43:56.783 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:843 - Found weight modifiers for transport: ['>32t']
2025-05-28 08:43:56.784 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:854 - Constructed dynamic filters with 4 conditions
2025-05-28 08:43:56.785 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:855 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöº Phase 1.5 - Dynamic Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
2025-05-28 08:43:56.786 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 08:43:56.787 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Heavy-duty road freight transportation services utilizing diesel-powered trucks with a gross vehicle weight exceeding 32 tonnes for the delivery of heavy cargo. Operations involve compression ignition engines optimized for long-haul logistics, focusing on the use phase emissions and energy consumption in commercial trucking fleets under ISIC sector 4923.'
2025-05-28 08:43:56.787 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '4923:Freight transport by road'}}, {'isic_section': {'$eq': 'H - Transportation and storage'}}, {'unit': {'$in': ['tkm']}}]}
2025-05-28 08:43:56.788 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 10 results
2025-05-28 08:43:56.788 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 0.07ms
2025-05-28 08:43:56.789 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 3 candidates
2025-05-28 08:43:56.789 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'transport, freight, lorry >32 metric ton' (distance: 0.1540, similarity: 0.8460)
2025-05-28 08:43:56.790 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 2: 'transport, freight, lorry 16-32 metric ton' (distance: 0.2670, similarity: 0.7330)
2025-05-28 08:43:56.790 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 3: 'transport, freight, lorry 7.5-16 metric ton' (distance: 0.3890, similarity: 0.6110)
2025-05-28 08:43:56.791 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 3 candidate objects
2025-05-28 08:43:56.791 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'transport, freight, lorry >32 metric ton' (distance: 0.1540)
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöì Phase 1.6 - Found 3 candidates
EINFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒîì Testing complete pipeline with geography fallback
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒô¥ Input: 'carbon steel production' (ISO: ZW)
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:43:58.356 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:44:01.037 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
INFO:httpx:HTTP Request: POST https://azureopenai-carbonbright-staging.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-05-28 08:44:03.278 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 08:44:03.278 | INFO     | emissions_factor_matching.predictions:augment_query_text:737 - Query augmentation successful: 'Hot-rolled carbon steel production for industrial applications, adhering to standard specifications in the construction materials sector. Manufacturing processes include steelmaking in blast furnaces and basic oxygen furnaces, followed by rolling and finishing operations to produce structural-grade steel products. Relevant to ISIC 2410, covering the manufacture of basic iron and steel.'
2025-05-28 08:44:03.286 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:854 - Constructed dynamic filters with 4 conditions
2025-05-28 08:44:03.286 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:855 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2410:Manufacture of basic iron and steel'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}, {'unit': {'$in': ['kg']}}]}
2025-05-28 08:44:03.287 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 08:44:03.287 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Hot-rolled carbon steel production for industrial applications, adhering to standard specifications in the construction materials sector. Manufacturing processes include steelmaking in blast furnaces and basic oxygen furnaces, followed by rolling and finishing operations to produce structural-grade steel products. Relevant to ISIC 2410, covering the manufacture of basic iron and steel.'
2025-05-28 08:44:03.288 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2410:Manufacture of basic iron and steel'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}, {'unit': {'$in': ['kg']}}]}
2025-05-28 08:44:03.288 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 10 results
2025-05-28 08:44:03.289 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 0.07ms
2025-05-28 08:44:03.289 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 1 candidates
2025-05-28 08:44:03.289 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'steel production, carbon steel, hot-rolled' (distance: 0.2340, similarity: 0.7660)
2025-05-28 08:44:03.290 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 1 candidate objects
2025-05-28 08:44:03.290 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'steel production, carbon steel, hot-rolled' (distance: 0.2340)
EINFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:≡ƒöä Testing pipeline data flow integrity
INFO:emissions_factor_matching.tests.test_phase1_9_complete_integration:Γ£à Pipeline data flow integrity test passed!
.
======================================================================
ERROR: test_complete_pipeline_phases_1_through_9 (emissions_factor_matching.tests.test_phase1_9_complete_integration.TestPhase1to9CompleteIntegration)
Test complete pipeline from Phase 1.1 through Phase 1.9 including geography matching and response assembly
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/unittest/mock.py", line 1379, in patched
    return func(*newargs, **newkeywargs)
  File "/home/<USER>/app/emissions_factor_matching/tests/test_phase1_9_complete_integration.py", line 175, in test_complete_pipeline_phases_1_through_9
    matched_ef = re_rank_candidates(
  File "/home/<USER>/app/emissions_factor_matching/predictions.py", line 604, in re_rank_candidates
    prompt = get_llm_reranking_prompt(
TypeError: get_llm_reranking_prompt() got an unexpected keyword argument 'user_query'

======================================================================
ERROR: test_complete_pipeline_with_geography_fallback (emissions_factor_matching.tests.test_phase1_9_complete_integration.TestPhase1to9CompleteIntegration)
Test complete pipeline with geography fallback scenario
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/unittest/mock.py", line 1379, in patched
    return func(*newargs, **newkeywargs)
  File "/home/<USER>/app/emissions_factor_matching/tests/test_phase1_9_complete_integration.py", line 347, in test_complete_pipeline_with_geography_fallback
    matched_ef = re_rank_candidates(request, candidates, augmented_query, enhanced_category, modifiers, isic_codes)
  File "/home/<USER>/app/emissions_factor_matching/predictions.py", line 604, in re_rank_candidates
    prompt = get_llm_reranking_prompt(
TypeError: get_llm_reranking_prompt() got an unexpected keyword argument 'user_query'

----------------------------------------------------------------------
Ran 3 tests in 13.876s

FAILED (errors=2)
load INSTRUCTOR_Transformer
max_seq_length  512
load INSTRUCTOR_Transformer
max_seq_length  512
Token has not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: read).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
